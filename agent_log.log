2025-08-19 16:23:36,211 - INFO - ✅ LLM (glm-4.5) 初始化成功。
2025-08-19 16:23:36,212 - INFO - 🧹 检测到旧的历史记录目录，正在清理 './chroma_db'...
2025-08-19 16:23:36,212 - INFO - ✅ 旧的历史记录已成功清理。
2025-08-19 16:23:36,666 - INFO - ✅ 向量化历史管理器初始化成功，数据将持久化到 './chroma_db' (遥测已禁用)。
2025-08-19 16:23:36,784 - INFO - ✅ 成功加载 3 个MCP工具
2025-08-19 16:23:36,793 - INFO - 
--- 自主智能体已准备就绪 ---
2025-08-19 16:23:39,641 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 16:23:42,882 - INFO - 🛠️ 工具调用: NewTask 参数: {"task_description": "初始化目录C:\\Users\\<USER>\\Desktop\\dify-main项目，分析项目结构", "Additional_information": "这是项目审计的第一步，需要：\n1. 列出项目根目录的所有文件和文件夹\n2. 以树状结构递归显示整个项目的目录结构\n3. 分析项目的主要组成部分和整体架构\n4. 识别项目的技术栈和框架类型\n5. 为后续的详细审计提供项目概览\n\n请确保获得完整的项目结构信息，这是后续所有审计工作的基础。"}
2025-08-19 16:23:42,883 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 1444, Completion: 204, Total: 1648
2025-08-19 16:23:42,912 - INFO - NewTask called with: 初始化目录C:\Users\<USER>\Desktop\dify-main项目，分析项目结构
2025-08-19 16:23:42,913 - INFO - 
📋 [TaskStack] 添加单个任务
2025-08-19 16:23:42,914 - INFO -    详情: 任务: 初始化目录C:\Users\<USER>\Desktop\dify-main项目，分析项目结构
2025-08-19 16:23:42,914 - INFO -    📚 当前任务树 (共 1 个任务):
2025-08-19 16:23:42,914 - INFO - └── 🔄 初始化目录C:\Users\<USER>\Desktop\dify-main项目，分析项目结构 (ID: f2d031bf)  <== [正在执行]
2025-08-19 16:23:42,915 - INFO - 
2025-08-19 16:23:42,915 - INFO - 🧠 正在为新任务 '初始化目录C:\Users\<USER>\Desktop\dify-main项目，分析项目结构...' 检索历史经验...
2025-08-19 16:23:43,290 - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/embeddings "HTTP/1.1 200 OK"
2025-08-19 16:23:43,315 - INFO - 📂 未找到相关历史记录。
2025-08-19 16:23:47,560 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 16:23:49,859 - INFO - 🛠️ [子孙智能体]工具调用: list_directory 参数: {"directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main"}
2025-08-19 16:23:49,859 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 10469, Completion: 155, Total: 10624
2025-08-19 16:23:52,795 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 16:23:54,037 - INFO - 🛠️ [子孙智能体]工具调用: list_directory 参数: {"directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main", "tree_view": true}
2025-08-19 16:23:54,037 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 10778, Completion: 71, Total: 10849
2025-08-19 16:24:05,810 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 16:24:06,726 - INFO - 🛠️ [子孙智能体]工具调用: read_file 参数: {"file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\package.json"}
2025-08-19 16:24:06,726 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 53194, Completion: 64, Total: 53258
2025-08-19 16:24:16,655 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 16:24:17,195 - INFO - 🛠️ [子孙智能体]工具调用: read_file 参数: {"file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\pyproject.toml"}
2025-08-19 16:24:17,195 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 53334, Completion: 52, Total: 53386
2025-08-19 16:24:26,011 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 16:24:27,062 - INFO - 🛠️ [子孙智能体]工具调用: read_file 参数: {"file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\web\\package.json"}
2025-08-19 16:24:27,063 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 56350, Completion: 47, Total: 56397
2025-08-19 16:24:29,587 - INFO - 
--- 流程执行完毕，正在清理所有异步资源 ---
2025-08-19 16:24:29,595 - INFO - 
--- Final Token Usage Summary ---
Model: glm-4.5
Total Prompt Tokens: 185569
Total Completion Tokens: 593
Total Tokens Used: 186162
---------------------------------
2025-08-19 16:24:29,595 - INFO - 
--- 所有资源已成功清理 ---
